{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 4156303299353468110, "deps": [[376837177317575824, "softbuffer", false, 15151319845919561101], [442785307232013896, "tauri_runtime", false, 12212259022337871714], [3150220818285335163, "url", false, 7653151978478686198], [3722963349756955755, "once_cell", false, 5482915910889426953], [4143744114649553716, "raw_window_handle", false, 7708125213860020614], [5986029879202738730, "log", false, 9780467864032512016], [7752760652095876438, "build_script_build", false, 11617338057458035829], [8539587424388551196, "webview2_com", false, 10319556161418693019], [9010263965687315507, "http", false, 10778138314687343874], [11050281405049894993, "tauri_utils", false, 4402207249847429009], [13223659721939363523, "tao", false, 12685501197773319642], [14585479307175734061, "windows", false, 4949495780415435215], [14794439852947137341, "wry", false, 18081686681129478790]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-3b42acf071dfeaf7\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}