@echo off
echo Starting Northen Star PWA...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not available
    pause
    exit /b 1
)

REM Check if package.json exists
if not exist "package.json" (
    echo Error: package.json not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo Installing dependencies (if needed)...
npm install

echo.
echo Building and starting PWA...
echo The app will be available at: http://localhost:8547
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the PWA server
npm run pwa:serve

pause
