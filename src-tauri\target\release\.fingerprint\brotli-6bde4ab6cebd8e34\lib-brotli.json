{"rustc": 16591470773350601817, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 7073890835992331790, "profile": 2040997289075261528, "path": 13987034635625629778, "deps": [[2568673438000342723, "brotli_decompressor", false, 9749896149530983565], [9611597350722197978, "alloc_no_stdlib", false, 2625431026266204969], [17470296833448545982, "alloc_stdlib", false, 7846988570486765089]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\brotli-6bde4ab6cebd8e34\\dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}