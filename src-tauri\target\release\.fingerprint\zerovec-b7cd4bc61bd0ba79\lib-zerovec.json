{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2040997289075261528, "path": 7603359693975987644, "deps": [[9620753569207166497, "zerovec_derive", false, 17516200571154585408], [10706449961930108323, "yoke", false, 5254732535595604806], [17046516144589451410, "zerofrom", false, 17794093669718809814]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zerovec-b7cd4bc61bd0ba79\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}