{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Northen Star", "version": "0.1.0", "identifier": "com.northenstar.app", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Northen Star", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}