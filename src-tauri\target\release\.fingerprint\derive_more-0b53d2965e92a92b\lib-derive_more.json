{"rustc": 16591470773350601817, "features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"not\", \"rustc_version\", \"sum\", \"try_into\", \"unwrap\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 1369601567987815722, "path": 12353550819620008072, "deps": [[3060637413840920116, "proc_macro2", false, 10089015578376569321], [10640660562325816595, "syn", false, 3420425040707328948], [14907448031486326382, "convert_case", false, 14775176393287722992], [17990358020177143287, "quote", false, 14342484963935004247]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\derive_more-0b53d2965e92a92b\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}