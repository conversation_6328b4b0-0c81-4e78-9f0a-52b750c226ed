"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Download, X } from "lucide-react";
import { useEffect, useState } from "react";

interface BeforeInstallPromptEvent extends Event {
	readonly platforms: string[];
	readonly userChoice: Promise<{
		outcome: "accepted" | "dismissed";
		platform: string;
	}>;
	prompt(): Promise<void>;
}

export function PWAInstall() {
	const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
	const [showInstallBanner, setShowInstallBanner] = useState(false);
	const [isInstalled, setIsInstalled] = useState(false);

	// Port configuration
	const PWA_PORT = 8547;

	useEffect(() => {
		// Check if app is already installed
		const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
		const isInWebAppiOS = (window.navigator as any).standalone === true;
		setIsInstalled(isStandalone || isInWebAppiOS);

		// Listen for the beforeinstallprompt event
		const handleBeforeInstallPrompt = (e: Event) => {
			e.preventDefault();
			setDeferredPrompt(e as BeforeInstallPromptEvent);
			setShowInstallBanner(true);
		};

		// Listen for app installed event
		const handleAppInstalled = () => {
			setIsInstalled(true);
			setShowInstallBanner(false);
			setDeferredPrompt(null);
		};

		window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
		window.addEventListener("appinstalled", handleAppInstalled);

		return () => {
			window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
			window.removeEventListener("appinstalled", handleAppInstalled);
		};
	}, []);

	const handleInstallClick = async () => {
		if (!deferredPrompt) return;

		deferredPrompt.prompt();
		const { outcome } = await deferredPrompt.userChoice;

		if (outcome === "accepted") {
			console.log("User accepted the install prompt");
		} else {
			console.log("User dismissed the install prompt");
		}

		setDeferredPrompt(null);
		setShowInstallBanner(false);
	};

	const handleDismiss = () => {
		setShowInstallBanner(false);
	};

	// Don't show if already installed or no prompt available
	if (isInstalled || !showInstallBanner || !deferredPrompt) {
		return null;
	}

	return (
		<div className="fixed top-4 left-4 right-4 z-50 bg-gradient-to-r from-teal-500 to-teal-600 text-white p-4 rounded-lg shadow-lg flex items-center justify-between">
			<div className="flex items-center space-x-3">
				<Download className="h-5 w-5" />
				<div>
					<p className="font-medium">Install Northen Star</p>
					<p className="text-sm opacity-90">Get the full desktop experience</p>
				</div>
			</div>
			<div className="flex items-center space-x-2">
				<Button
					onClick={handleInstallClick}
					variant="secondary"
					size="sm"
					className="bg-white text-teal-600 hover:bg-gray-100"
				>
					Install
				</Button>
				<Button onClick={handleDismiss} variant="ghost" size="sm" className="text-white hover:bg-white/20">
					<X className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
}
