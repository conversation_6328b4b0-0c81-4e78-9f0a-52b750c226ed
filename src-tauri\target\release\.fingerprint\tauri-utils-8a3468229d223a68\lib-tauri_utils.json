{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 17620442681569047700, "deps": [[561782849581144631, "html5ever", false, 11938631852885255654], [1200537532907108615, "url<PERSON><PERSON>n", false, 17252779169944526386], [3150220818285335163, "url", false, 7653151978478686198], [3191507132440681679, "serde_untagged", false, 8044430739327141654], [4899080583175475170, "semver", false, 14013045036108291583], [5578504951057029730, "serde_with", false, 3534917422151048687], [5986029879202738730, "log", false, 9780467864032512016], [6262254372177975231, "kuchiki", false, 7622478795269003333], [6606131838865521726, "ctor", false, 6378635709361389651], [7170110829644101142, "json_patch", false, 13628951526716896194], [8319709847752024821, "uuid", false, 952247417835830285], [9010263965687315507, "http", false, 10778138314687343874], [9451456094439810778, "regex", false, 6372726920460976763], [9689903380558560274, "serde", false, 17059111310074533947], [10806645703491011684, "thiserror", false, 3412994927594303587], [11989259058781683633, "dunce", false, 1196112619190832528], [13625485746686963219, "anyhow", false, 12519507365521421523], [14132538657330703225, "brotli", false, 3098188109207835277], [15367738274754116744, "serde_json", false, 2036542764797282122], [15609422047640926750, "toml", false, 1907485954181535850], [15622660310229662834, "walkdir", false, 12086629986551584551], [15932120279885307830, "memchr", false, 2756238601866959573], [17146114186171651583, "infer", false, 949331874504083525], [17155886227862585100, "glob", false, 12121957761906363650], [17186037756130803222, "phf", false, 11772994511194413194]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-8a3468229d223a68\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}