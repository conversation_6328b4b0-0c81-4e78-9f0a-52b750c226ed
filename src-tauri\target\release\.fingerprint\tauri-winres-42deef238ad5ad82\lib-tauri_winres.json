{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 1369601567987815722, "path": 14947360578207156231, "deps": [[13443124389817561858, "embed_resource", false, 4508444391416749523], [14483812548788871374, "indexmap", false, 1870806632105245164], [15609422047640926750, "toml", false, 9460031580210832492]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winres-42deef238ad5ad82\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}