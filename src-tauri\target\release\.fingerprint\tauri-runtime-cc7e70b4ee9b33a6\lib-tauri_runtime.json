{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 1776179443988225175, "deps": [[442785307232013896, "build_script_build", false, 15470258368559994425], [3150220818285335163, "url", false, 7653151978478686198], [4143744114649553716, "raw_window_handle", false, 7708125213860020614], [7606335748176206944, "dpi", false, 2782580307301169106], [9010263965687315507, "http", false, 10778138314687343874], [9689903380558560274, "serde", false, 17059111310074533947], [10806645703491011684, "thiserror", false, 3412994927594303587], [11050281405049894993, "tauri_utils", false, 4402207249847429009], [14585479307175734061, "windows", false, 4949495780415435215], [15367738274754116744, "serde_json", false, 2036542764797282122], [16727543399706004146, "cookie", false, 2655647101884397467]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-cc7e70b4ee9b33a6\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}