{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 14356718205376624770, "path": 15178118877330383945, "deps": [[253581978874359338, "deranged", false, 9034879099199852485], [724804171976944018, "num_conv", false, 16158107107671329431], [1509944293013079861, "time_macros", false, 18310935250205370153], [5901133744777009488, "powerfmt", false, 5617800763828633476], [7695812897323945497, "itoa", false, 16445087792208889065], [9886904983647127192, "time_core", false, 7043416511875277648]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\time-e5e7adb427834033\\dep-lib-time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}