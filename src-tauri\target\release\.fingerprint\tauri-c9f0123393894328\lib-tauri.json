{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 14642100191201630401, "deps": [[40386456601120721, "percent_encoding", false, 15195972064651261360], [442785307232013896, "tauri_runtime", false, 12212259022337871714], [1200537532907108615, "url<PERSON><PERSON>n", false, 17252779169944526386], [3150220818285335163, "url", false, 7653151978478686198], [4143744114649553716, "raw_window_handle", false, 7708125213860020614], [4341921533227644514, "muda", false, 8740980435086369828], [4919829919303820331, "serialize_to_javascript", false, 9171710624389533256], [5986029879202738730, "log", false, 9780467864032512016], [7752760652095876438, "tauri_runtime_wry", false, 15647328790409619169], [8539587424388551196, "webview2_com", false, 10319556161418693019], [9010263965687315507, "http", false, 10778138314687343874], [9228235415475680086, "tauri_macros", false, 13199014338524297027], [9538054652646069845, "tokio", false, 943889624393856239], [9689903380558560274, "serde", false, 17059111310074533947], [9920160576179037441, "getrandom", false, 7707427972266748450], [10229185211513642314, "mime", false, 7594425863437882380], [10629569228670356391, "futures_util", false, 4942355853786420523], [10755362358622467486, "build_script_build", false, 13620538293972922491], [10806645703491011684, "thiserror", false, 3412994927594303587], [11050281405049894993, "tauri_utils", false, 4402207249847429009], [11989259058781683633, "dunce", false, 1196112619190832528], [12565293087094287914, "window_vibrancy", false, 10318666680286188216], [12986574360607194341, "serde_repr", false, 5760598007399883519], [13077543566650298139, "heck", false, 3229411370429346175], [13625485746686963219, "anyhow", false, 12519507365521421523], [14585479307175734061, "windows", false, 4949495780415435215], [15367738274754116744, "serde_json", false, 2036542764797282122], [16928111194414003569, "dirs", false, 14523640803555100537], [17155886227862585100, "glob", false, 12121957761906363650]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-c9f0123393894328\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}