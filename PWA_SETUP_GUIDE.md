# 🌟 Northen Star PWA Setup Guide

## The PWA Restart Issue

When you restart your PC, the PWA stops working because the development server (localhost:8547) is no longer running. This is normal behavior for development PWAs.

## 🚀 Quick Solutions

### Option 1: Use the Startup Scripts (Recommended)

**For Windows:**
1. Double-click `start-pwa.bat` in the project folder
2. The script will automatically start the server
3. Your PWA will work again at http://localhost:8547

**For PowerShell users:**
1. Right-click `start-pwa.ps1` → "Run with PowerShell"
2. Or open PowerShell in the project folder and run: `.\start-pwa.ps1`

### Option 2: Manual Terminal Commands

Open terminal in the project folder and run:

**For Development (with hot reload):**
```bash
npm run pwa:dev
```

**For Production (recommended for daily use):**
```bash
npm run pwa:serve
```

## 🔧 Making PWA Auto-Start (Advanced)

### Windows Task Scheduler Method

1. Open Task Scheduler (search "Task Scheduler" in Start menu)
2. Click "Create Basic Task"
3. Name: "Northen Star PWA"
4. Trigger: "When I log on"
5. Action: "Start a program"
6. Program: `cmd.exe`
7. Arguments: `/c "cd /d "C:\path\to\your\project" && npm run pwa:serve"`
8. Replace the path with your actual project path

### Windows Startup Folder Method

1. Press `Win + R`, type `shell:startup`, press Enter
2. Create a shortcut to `start-pwa.bat` in this folder
3. The PWA will start automatically when Windows starts

## 📱 Better Alternative: Use the Android App

The Android APK doesn't have this issue because it's a standalone app:
- Location: `android/app/build/outputs/apk/debug/app-debug.apk`
- Install on your Android device or emulator
- Works offline and doesn't need a server

## 🛠️ Troubleshooting

### PWA Shows "This site can't be reached"
- **Cause:** Development server is not running
- **Solution:** Run one of the startup scripts or manual commands above

### "Port 8547 is already in use"
- **Cause:** Another instance is already running
- **Solution:** Close other terminals or change port in package.json

### Service Worker Issues
- **Solution:** Clear browser cache and reload
- In Chrome: F12 → Application → Storage → Clear storage

### PWA Not Installing
- **Cause:** Browser doesn't recognize it as installable
- **Solution:** Make sure you're using Chrome/Edge and the server is running

## 🎯 Recommended Workflow

1. **Daily Use:** Use `start-pwa.bat` to quickly start the app
2. **Development:** Use `npm run pwa:dev` for making changes
3. **Mobile:** Use the Android APK for mobile devices
4. **Offline:** The PWA will show helpful instructions when offline

## 📋 Port Information

- **PWA Port:** 8547 (chosen to avoid conflicts)
- **Development Port:** 3000 (Next.js default)
- **Production PWA:** 8547 (serves built files)

## 🔄 Updates

When you make changes to the app:
1. Stop the current server (Ctrl+C)
2. Run the startup script again
3. The PWA will update automatically
