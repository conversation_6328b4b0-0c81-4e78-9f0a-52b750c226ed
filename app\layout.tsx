import type { Metadata, Viewport } from "next";
import "./globals.css";
import "./pwa-styles.css";

export const viewport: Viewport = {
	width: "device-width",
	initialScale: 1,
	maximumScale: 1,
	userScalable: false,
	themeColor: "#0f172a",
	viewportFit: "cover",
};

export const metadata: Metadata = {
	title: "Northen Star",
	description: "Daily planner and productivity app to guide your daily goals",
	generator: "v0.dev",
	manifest: "/manifest.json",
	icons: {
		icon: [{ url: "/logo.svg", type: "image/svg+xml", sizes: "any" }],
		shortcut: "/logo.svg",
		apple: [{ url: "/logo.svg", sizes: "180x180" }],
	},
	appleWebApp: {
		capable: true,
		statusBarStyle: "default",
		title: "Northen Star",
	},
	other: {
		"mobile-web-app-capable": "yes",
		"apple-mobile-web-app-capable": "yes",
		"apple-mobile-web-app-status-bar-style": "black-translucent",
		"apple-mobile-web-app-title": "Northen Star",
		"application-name": "Northen Star",
		"msapplication-TileColor": "#0f172a",
		"msapplication-config": "none",
		"msapplication-navbutton-color": "#0f172a",
		"msapplication-window": "width=768;height=600",
		"format-detection": "telephone=no",
		"msapplication-starturl": "/",
		"msapplication-tap-highlight": "no",
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<head>
				<meta name="theme-color" content="#0f172a" />
				<meta name="msapplication-navbutton-color" content="#0f172a" />
				<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
				<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
				<script
					dangerouslySetInnerHTML={{
						__html: `
							// Service Worker registration
							if ('serviceWorker' in navigator) {
								window.addEventListener('load', function() {
									navigator.serviceWorker.register('/sw.js')
										.then(function(registration) {
											console.log('SW registered: ', registration);
										})
										.catch(function(registrationError) {
											console.log('SW registration failed: ', registrationError);
										});
								});
							}

							// Set default window size for PWA
							window.addEventListener('load', function() {
								if (window.matchMedia('(display-mode: standalone)').matches) {
									// Default size: minimum before mobile layout (768px width)
									const defaultWidth = 768;
									const defaultHeight = 700;

									// Only resize if window is smaller than default
									if (window.outerWidth < defaultWidth || window.outerHeight < defaultHeight) {
										try {
											window.resizeTo(defaultWidth, defaultHeight);
										} catch (e) {
											// Resize might not be allowed in some browsers
											console.log('Window resize not permitted');
										}
									}
								}
							});
						`,
					}}
				/>
			</head>
			<body>{children}</body>
		</html>
	);
}
