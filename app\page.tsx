import { BrowserRecommendation } from "@/components/browser-recommendation";
import DailyPlanner from "@/components/daily-planner";
import { PWAInstall } from "@/components/pwa-install";

export default function Home() {
	return (
		<main className="min-h-screen bg-gradient-to-br from-gray-950 to-gray-900 text-gray-200 relative">
			<div className="fixed inset-0 bg-gradient-to-br from-gray-950 to-gray-900 -z-10"></div>
			<BrowserRecommendation />
			<PWAInstall />
			<DailyPlanner />
		</main>
	);
}
