{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 11098224100583484512, "deps": [[376837177317575824, "build_script_build", false, 15980713945062072526], [4143744114649553716, "raw_window_handle", false, 7708125213860020614], [5986029879202738730, "log", false, 9780467864032512016], [10281541584571964250, "windows_sys", false, 5279027709862169213]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-2fb69eefc318208d\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}