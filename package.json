{"name": "northen-star", "version": "0.1.0", "description": "Northen Star - A beautiful productivity app with cross-platform support", "author": "<PERSON>", "private": true, "homepage": "./", "main": "electron-main.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:mobile": "next build && npx cap sync", "build:electron": "next build && npx cap sync @capacitor-community/electron", "android:dev": "npm run build:mobile && npx cap run android", "android:build": "npm run build:mobile && npx cap build android", "pwa:build": "npm run build", "pwa:serve": "npm run build && npx serve out -p 8547", "pwa:dev": "next dev -p 8547", "tauri": "npx tauri", "tauri:dev": "npx tauri dev", "tauri:build": "npx tauri build", "desktop:dev": "npx tauri dev", "desktop:build": "npx tauri build", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "desktop": "npm run electron:dev", "desktop:pack": "npm run electron:pack"}, "dependencies": {"@capacitor-community/electron": "^5.0.1", "@capacitor/android": "^7.4.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/supabase-js": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.0.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "electron": "^36.5.0", "electron-builder": "^26.0.12", "postcss": "^8.5", "serve": "^14.2.4", "tailwindcss": "^3.4.17", "typescript": "^5", "wait-on": "^8.0.3"}, "build": {"appId": "com.northenstar.app", "productName": "Northen Star", "directories": {"output": "dist"}, "files": ["out/**/*", "electron-main.js", "package.json"], "extraResources": [{"from": "public", "to": "public"}], "win": {"target": "nsis", "icon": "public/logo.png"}, "mac": {"target": "dmg", "icon": "public/logo.png"}, "linux": {"target": "AppImage", "icon": "public/logo.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}